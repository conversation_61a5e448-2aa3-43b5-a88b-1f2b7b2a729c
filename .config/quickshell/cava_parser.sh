#!/bin/bash

# Create cava config for visual output
cat > /tmp/cava_visual.conf << 'EOF'
[general]
bars = 7
framerate = 30
sensitivity = 100

[input]
method = pulse
source = auto

[output]
method = ncurses
channels = mono

[color]
gradient = 0

[smoothing]
monstercat = 0
waves = 0
noise_reduction = 0.5
EOF

# Run cava and parse its visual output
cava -p /tmp/cava_visual.conf | while IFS= read -r line; do
    # Extract bar heights from cava's visual output
    # Convert Unicode block characters to numeric values
    echo "$line" | sed 's/▁/1/g; s/▂/2/g; s/▃/3/g; s/▄/4/g; s/▅/5/g; s/▆/6/g; s/▇/7/g; s/█/8/g; s/[^1-8]//g' | \
    fold -w1 | head -7 | tr '\n' ';' | sed 's/;$//'
    echo
done
