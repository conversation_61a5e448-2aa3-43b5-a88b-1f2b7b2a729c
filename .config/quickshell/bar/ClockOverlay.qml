import QtQuick
import Quickshell
import Quickshell.Wayland

PanelWindow {
    id: overlayWindow
    
    property var barWindow: null
    property bool overlayVisible: false
    property string overlayMode: "none"
    
    // Pre-calculated overlay dimensions
    readonly property var overlayDimensions: {
        "smallCalendar": { width: 280, height: 220 },
        "expandedTime": { width: 300, height: 100 },
        "fullCalendar": { width: 400, height: 320 }
    }
    
    // Animation properties
    property int animDuration: 300
    property bool isAnimating: false
    
    // Position as a small centered window
    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    // Start small at bar height, morph to target size
    implicitWidth: overlayVisible ? (overlayDimensions[overlayMode]?.width || 280) : 100
    implicitHeight: overlayVisible ? (overlayDimensions[overlayMode]?.height || 220) : 0

    // Center the overlay horizontally with calculated margins
    margins {
        top: 30  // Just below the bar
        left: Math.max(0, (1920 - implicitWidth) / 2)  // Center on main screen
        right: Math.max(0, (1920 - implicitWidth) / 2)  // Center on main screen
    }

    // Only visible when overlay is active
    visible: overlayVisible

    // Layer shell properties
    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusionMode: ExclusionMode.Ignore
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.None
    
    // Smooth morphing animations
    Behavior on width {
        NumberAnimation {
            duration: animDuration
            easing.type: Easing.OutBack
        }
    }
    
    Behavior on height {
        NumberAnimation {
            duration: animDuration
            easing.type: Easing.OutBack
        }
    }
    

    
    // Trigger animation state
    onOverlayVisibleChanged: {
        if (overlayVisible) {
            isAnimating = true
            animationTimer.restart()
        }
    }
    
    Timer {
        id: animationTimer
        interval: animDuration + 50
        onTriggered: overlayWindow.isAnimating = false
    }
    
    // Connector that makes it look like it's growing from the bar
    Rectangle {
        id: connector
        visible: overlayVisible

        // Position at the top center of the overlay
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.top: parent.top
        anchors.topMargin: -10  // Overlap with bar

        width: 60
        height: 20
        color: "#1a1a1a"  // Same as overlay

        // Curved connector shape
        radius: 10

        // Make it look connected
        border.width: 2
        border.color: "#00bcd4"
    }

    // Main overlay content
    Rectangle {
        id: overlayContent
        anchors.fill: parent

        color: "#1a1a1a"
        radius: 16
        border.width: 2
        border.color: "#00bcd4"  // Cyan

        // Debug border
        Rectangle {
            anchors.fill: parent
            color: "transparent"
            border.width: 1
            border.color: "red"
            radius: parent.radius
        }
        
        // Content area
        Item {
            id: contentArea
            anchors.fill: parent
            anchors.margins: 16
            
            // Debug text
            Text {
                anchors.top: parent.top
                anchors.left: parent.left
                text: "Overlay Mode: " + overlayMode + " | Size: " + overlayWindow.width + "x" + overlayWindow.height
                color: "lime"
                font.pixelSize: 10
                z: 1000
            }
            
            // Small calendar content
            Column {
                visible: overlayMode === "smallCalendar"
                anchors.centerIn: parent
                spacing: 12

                Text {
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: Qt.formatDateTime(new Date(), "MMMM yyyy")
                    color: "#ffffff"
                    font.pixelSize: 16
                    font.weight: Font.Medium
                }

                Grid {
                    anchors.horizontalCenter: parent.horizontalCenter
                    columns: 7
                    spacing: 8

                    Repeater {
                        model: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
                        Text {
                            text: modelData
                            color: "#cccccc"
                            font.pixelSize: 10
                            horizontalAlignment: Text.AlignHCenter
                            width: 24
                        }
                    }

                    Repeater {
                        model: 35
                        Rectangle {
                            width: 24
                            height: 20
                            color: index === 20 ? "#00bcd4" : "transparent"
                            radius: 4

                            Text {
                                anchors.centerIn: parent
                                text: ((index - 6) % 31) + 1
                                color: parent.color === "transparent" ? "#cccccc" : "#000000"
                                font.pixelSize: 10
                                visible: index > 5 && index < 32
                            }
                        }
                    }
                }

                Rectangle {
                    anchors.horizontalCenter: parent.horizontalCenter
                    width: 120
                    height: 28
                    color: "#00bcd4"
                    radius: 14

                    Text {
                        anchors.centerIn: parent
                        text: "Expand Year View"
                        color: "#000000"
                        font.pixelSize: 11
                        font.weight: Font.Medium
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            overlayMode = "fullCalendar"
                        }
                    }
                }
            }
            
            // Expanded time content
            Row {
                visible: overlayMode === "expandedTime"
                anchors.centerIn: parent
                spacing: 20

                Column {
                    spacing: 4

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: Qt.formatDateTime(new Date(), "dd")
                        color: "#ffffff"
                        font.pixelSize: 48
                        font.weight: Font.Bold
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "•••"
                        color: "#00bcd4"
                        font.pixelSize: 16
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: Qt.formatDateTime(new Date(), "MM")
                        color: "#ffffff"
                        font.pixelSize: 32
                        font.weight: Font.Bold
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: Qt.formatDateTime(new Date(), "ddd, d")
                        color: "#cccccc"
                        font.pixelSize: 12
                    }
                }

                Column {
                    anchors.verticalCenter: parent.verticalCenter
                    spacing: 4

                    Text {
                        text: Qt.formatDateTime(new Date(), "hh:mm")
                        color: "#ffffff"
                        font.pixelSize: 24
                        font.weight: Font.Medium
                    }

                    Text {
                        text: Qt.formatDateTime(new Date(), "AP")
                        color: "#cccccc"
                        font.pixelSize: 14
                    }
                }
            }
            
            // Full calendar content
            Text {
                visible: overlayMode === "fullCalendar"
                anchors.centerIn: parent
                text: "Full Calendar View\n(Coming Soon)"
                color: "#ffffff"
                font.pixelSize: 18
                horizontalAlignment: Text.AlignHCenter
            }
        }
    }
}
