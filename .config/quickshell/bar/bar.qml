import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: window

    // Properties for multi-monitor support
    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0

    // Clock overlay states
    property bool showSmallCalendar: false
    property bool showExpandedTime: false
    property bool showFullCalendar: false

    screen: targetScreen

    // Configuration
    BarConfig {
        id: barConfig
    }

    // Theme instance
    Theme {
        id: theme
    }

    property var currentConfig: barConfig.getScreenConfig(screenIndex)
    
    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    // Straight bar - no margins, edge to edge
    implicitHeight: 30
    
    visible: true
    color: "transparent"

    Component.onCompleted: {
        console.log("=== BAR DEBUG ===")
        console.log("Bar created for screen:", targetScreen.name, "Index:", screenIndex)
        console.log("Bar visible:", visible)
        console.log("Bar width:", width, "height:", implicitHeight)
        console.log("Current config:", JSON.stringify(currentConfig))
        console.log("================")
    }
    
    // Main straight bar
    Rectangle {
        id: bar
        anchors.fill: parent

        // Straight edges - no radius
        radius: 0

        // Solid black background from theme
        color: theme.background

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 16
            anchors.rightMargin: 16
            anchors.topMargin: 6
            anchors.bottomMargin: 6
            spacing: 20

            // Left section
            Row {
                Layout.alignment: Qt.AlignLeft
                spacing: 10

                Repeater {
                    model: currentConfig.left

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex
                            }
                        }
                    }
                }
            }

            // Center section
            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignHCenter

                Row {
                    anchors.centerIn: parent
                    spacing: 10

                    Repeater {
                        model: currentConfig.center

                        Loader {
                            source: getModuleSource(modelData)
                            onLoaded: {
                                if (modelData === "workspaces" && item) {
                                    item.screenIndex = window.screenIndex
                                }
                                if (modelData === "clock" && item) {
                                    console.log("Clock module loaded, connecting to integrated overlay...")

                                    // Connect clock signals to integrated overlay
                                    item.showSmallCalendarRequested.connect(function() {
                                        console.log("Small calendar requested - toggling overlay")
                                        window.showSmallCalendar = !window.showSmallCalendar
                                        window.showExpandedTime = false
                                        window.showFullCalendar = false
                                    })

                                    item.showExpandedTimeRequested.connect(function() {
                                        console.log("Expanded time requested - toggling overlay")
                                        window.showExpandedTime = !window.showExpandedTime
                                        window.showSmallCalendar = false
                                        window.showFullCalendar = false
                                    })

                                    item.showFullCalendarRequested.connect(function() {
                                        console.log("Full calendar requested - toggling overlay")
                                        window.showFullCalendar = !window.showFullCalendar
                                        window.showSmallCalendar = false
                                        window.showExpandedTime = false
                                    })

                                    console.log("Clock signals connected successfully!")
                                }
                            }
                        }
                    }
                }
            }

            // Right section
            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 10

                Repeater {
                    model: currentConfig.right

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex
                            }
                        }
                    }
                }
            }
        }
    }  // End main bar Rectangle

    // Expose overlay state for external overlay window
    property bool overlayVisible: showSmallCalendar || showExpandedTime || showFullCalendar
    property string overlayMode: {
        if (showSmallCalendar) return "smallCalendar"
        if (showExpandedTime) return "expandedTime"
        if (showFullCalendar) return "fullCalendar"
        return "none"
    }



    // Helper function to get module source
    function getModuleSource(moduleName) {
        var moduleComponent = barConfig.availableModules[moduleName]
        if (moduleComponent) {
            return "./modules/" + moduleComponent + ".qml"
        }
        return ""
    }
}
