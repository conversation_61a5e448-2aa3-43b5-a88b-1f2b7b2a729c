import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: window

    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0
    property bool showMprisOverlay: false

    screen: targetScreen

    BarConfig {
        id: barConfig
    }

    Theme {
        id: theme
    }

    property var currentConfig: barConfig.getScreenConfig(screenIndex)

    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    implicitHeight: 30
    visible: true
    color: "transparent"

    Component.onCompleted: {
    }

    Rectangle {
        id: bar
        anchors.fill: parent
        radius: 0
        color: theme.background

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 16
            anchors.rightMargin: 16
            anchors.topMargin: 6
            anchors.bottomMargin: 6
            spacing: 20

            Row {
                Layout.alignment: Qt.AlignLeft
                spacing: 10

                Repeater {
                    model: currentConfig.left

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex
                            }
                        }
                    }
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignHCenter

                Row {
                    anchors.centerIn: parent
                    spacing: 0

                    Text {
                        text: "【"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Row {
                        id: centerContent
                        spacing: 10
                        anchors.verticalCenter: parent.verticalCenter

                        Repeater {
                            model: currentConfig.center

                            Loader {
                                source: getModuleSource(modelData)
                                onLoaded: {
                                    if (modelData === "workspaces" && item) {
                                        item.screenIndex = window.screenIndex
                                    }
                                    if (modelData === "mpris" && item) {
                                        item.showMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = !window.showMprisOverlay
                                        })

                                        item.hideMprisOverlayRequested.connect(function() {
                                            window.showMprisOverlay = false
                                        })
                                    }

                                }
                            }
                        }
                    }

                    Text {
                        text: "】"
                        color: theme.accent
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0

                Text {
                    text: "【"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Row {
                    id: rightContent
                    spacing: 10
                    anchors.verticalCenter: parent.verticalCenter

                    Repeater {
                        model: currentConfig.right

                        Loader {
                            source: getModuleSource(modelData)
                            onLoaded: {
                                if (modelData === "workspaces" && item) {
                                    item.screenIndex = window.screenIndex
                                }

                            }
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme.accent
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }

    property bool mprisOverlayVisible: showMprisOverlay

    function getModuleSource(moduleName) {
        var moduleComponent = barConfig.availableModules[moduleName]
        if (moduleComponent) {
            return "./modules/" + moduleComponent + ".qml"
        }
        return ""
    }
}
