import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.Pipewire
import "./modules"

PanelWindow {
    id: volumeOverlay
    objectName: "volumeOverlay"

    property var screen: Quickshell.screens[0]
    property var barWindow: null
    property bool overlayVisible: false

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property int volumePercent: audioNode ? Math.round(audioNode.volume * 100) : 0
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    visible: overlayVisible && audioSink !== null

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    width: 300
    height: 120

    x: barWindow ? (barWindow.x + barWindow.width - width - 20) : 0
    y: barWindow ? (barWindow.y + barWindow.height + 10) : 0

    color: "transparent"

    Theme {
        id: theme
    }

    Rectangle {
        id: overlayBackground
        anchors.fill: parent
        radius: 12
        color: theme.backgroundSecondary
        border.color: theme.accent
        border.width: 1

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15

            RowLayout {
                Layout.fillWidth: true
                spacing: 12

                Text {
                    id: volumeIcon
                    text: {
                        if (isMuted) return "󰖁"
                        if (volumePercent === 0) return "󰕿"
                        if (volumePercent < 30) return "󰖀"
                        if (volumePercent < 70) return "󰕾"
                        return "󰕾"
                    }
                    color: isMuted ? theme.textSecondary : theme.textPrimary
                    font.pixelSize: 24
                    font.family: "JetBrains Mono Nerd Font, monospace"
                }

                Text {
                    text: "Volume"
                    color: theme.textPrimary
                    font.pixelSize: 16
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                }

                Item { Layout.fillWidth: true }

                Text {
                    text: volumePercent + "%"
                    color: theme.accent
                    font.pixelSize: 18
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Bold
                }
            }

            Rectangle {
                id: sliderTrack
                Layout.fillWidth: true
                height: 8
                radius: 4
                color: theme.backgroundTertiary

                Rectangle {
                    id: sliderFill
                    width: parent.width * (volumePercent / 100)
                    height: parent.height
                    radius: parent.radius
                    color: isMuted ? theme.textSecondary : theme.accent

                    Behavior on width {
                        NumberAnimation { duration: 150 }
                    }

                    Behavior on color {
                        ColorAnimation { duration: 200 }
                    }
                }

                Rectangle {
                    id: sliderHandle
                    width: 16
                    height: 16
                    radius: 8
                    color: isMuted ? theme.textSecondary : theme.accent
                    border.color: theme.backgroundPrimary
                    border.width: 2
                    x: Math.max(0, Math.min(parent.width - width, (parent.width - width) * (volumePercent / 100)))
                    y: (parent.height - height) / 2

                    Behavior on x {
                        NumberAnimation { duration: 150 }
                    }

                    Behavior on color {
                        ColorAnimation { duration: 200 }
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true

                    onPressed: function(mouse) {
                        updateVolume(mouse.x)
                    }

                    onPositionChanged: function(mouse) {
                        if (pressed) {
                            updateVolume(mouse.x)
                        }
                    }

                    function updateVolume(x) {
                        if (audioNode) {
                            var newVolume = Math.max(0, Math.min(1, x / width))
                            audioNode.volume = newVolume
                        }
                    }
                }
            }

            RowLayout {
                Layout.fillWidth: true
                spacing: 10

                Rectangle {
                    width: 80
                    height: 32
                    radius: 6
                    color: muteMouseArea.containsMouse ? theme.accent : theme.backgroundTertiary
                    border.color: theme.accent
                    border.width: 1

                    Text {
                        anchors.centerIn: parent
                        text: isMuted ? "Unmute" : "Mute"
                        color: muteMouseArea.containsMouse ? theme.backgroundPrimary : theme.textPrimary
                        font.pixelSize: 11
                        font.family: "JetBrains Mono, monospace"
                        font.weight: Font.Medium
                    }

                    MouseArea {
                        id: muteMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onClicked: {
                            if (audioNode) {
                                audioNode.muted = !audioNode.muted
                            }
                        }
                    }
                }

                Item { Layout.fillWidth: true }

                Text {
                    text: audioSink ? audioSink.description : "No Audio Device"
                    color: theme.textSecondary
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                    elide: Text.ElideRight
                    Layout.maximumWidth: 150
                }
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            overlayVisible = false
        }
    }


}
