import QtQuick

Rectangle {
    id: clockModule
    objectName: "clockModule"
    width: clockText.width + 20
    height: 24
    radius: 6
    color: "transparent"

    // Signal to communicate with overlay
    signal showSmallCalendarRequested()
    signal showExpandedTimeRequested()
    signal showFullCalendarRequested()
    signal hideAllPopupsRequested()

    // Position info for overlay
    readonly property var clockPosition: ({
        x: clockModule.mapToGlobal(0, 0).x,
        y: clockModule.mapToGlobal(0, 0).y,
        width: clockModule.width,
        height: clockModule.height
    })
    property date currentDate: new Date()

    // Theme instance
    Theme {
        id: theme
    }

    Text {
        id: clockText
        anchors.centerIn: parent
        text: Qt.formatDateTime(currentDate, "h:mm AP")
        color: theme.textPrimary
        font.pixelSize: 12
        font.family: "JetBrains Mono, monospace"
        font.weight: Font.Medium
    }

    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            console.log("Clock clicked with button:", mouse.button)
            if (mouse.button === Qt.LeftButton) {
                console.log("Left click - requesting small calendar")
                showSmallCalendarRequested()
            } else if (mouse.button === Qt.RightButton) {
                console.log("Right click - requesting expanded time")
                showExpandedTimeRequested()
            }
        }

        onWheel: function(wheel) {
            console.log("Wheel event:", wheel.angleDelta.y)
            if (wheel.angleDelta.y > 0) {
                console.log("Scroll up - requesting small calendar")
                showSmallCalendarRequested()
            }
        }

        onEntered: {
            clockModule.color = Qt.rgba(0.3, 0.3, 0.3, 0.8)
        }

        onExited: {
            // No highlight effect
        }
    }


    // Update timer
    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }
}
