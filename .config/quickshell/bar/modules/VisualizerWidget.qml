import QtQuick
import QtQuick.Layouts

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int barCount: 20
    property int maxBarHeight: 12
    property var audioLevels: []
    property bool isActive: true

    Component.onCompleted: {
        generateRandomLevels()
        animationTimer.start()
    }

    RowLayout {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 1

        Repeater {
            id: barsRepeater
            model: barCount

            Rectangle {
                id: bar
                width: 2
                height: 2
                radius: 1
                color: theme.accent
                opacity: 0.8

                property real targetHeight: 2
                property real currentLevel: 0

                Behavior on height {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on opacity {
                    NumberAnimation {
                        duration: 200
                    }
                }

                function updateBar(level) {
                    currentLevel = level
                    targetHeight = Math.max(2, level * maxBarHeight)
                    height = targetHeight
                    opacity = 0.4 + (level * 0.6)
                }
            }
        }
    }

    Timer {
        id: animationTimer
        interval: 100
        repeat: true
        running: isActive

        onTriggered: {
            generateRandomLevels()
            updateBars()
        }
    }

    function generateRandomLevels() {
        audioLevels = []
        for (let i = 0; i < barCount; i++) {
            let baseLevel = Math.random() * 0.8
            let waveEffect = Math.sin((Date.now() / 200 + i * 0.3)) * 0.3 + 0.5
            let level = Math.max(0.1, Math.min(1.0, baseLevel * waveEffect))
            audioLevels.push(level)
        }
    }

    function updateBars() {
        for (let i = 0; i < barsRepeater.count; i++) {
            let bar = barsRepeater.itemAt(i)
            if (bar && i < audioLevels.length) {
                bar.updateBar(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < barsRepeater.count; i++) {
                let bar = barsRepeater.itemAt(i)
                if (bar) {
                    bar.opacity = 1.0
                }
            }
        }
        onExited: {
            for (let i = 0; i < barsRepeater.count; i++) {
                let bar = barsRepeater.itemAt(i)
                if (bar) {
                    bar.opacity = 0.4 + (bar.currentLevel * 0.6)
                }
            }
        }
    }
}
