import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Pipewire

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int barCount: 20
    property int maxBarHeight: 12
    property var audioLevels: []
    property bool isActive: true

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property real currentVolume: audioNode ? audioNode.volume : 0.0
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    PwObjectTracker {
        objects: [audioSink]
    }

    Component.onCompleted: {
        initializeAudioLevels()
        animationTimer.start()
    }

    RowLayout {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 1

        Repeater {
            id: barsRepeater
            model: barCount

            Rectangle {
                id: bar
                width: 2
                height: 2
                radius: 1
                color: theme.accent
                opacity: 0.8

                property real targetHeight: 2
                property real currentLevel: 0

                Behavior on height {
                    NumberAnimation {
                        duration: 150
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on opacity {
                    NumberAnimation {
                        duration: 200
                    }
                }

                function updateBar(level) {
                    currentLevel = level
                    targetHeight = Math.max(2, level * maxBarHeight)
                    height = targetHeight

                    if (isMuted) {
                        opacity = 0.2
                        color = theme.textSecondary
                    } else {
                        opacity = 0.3 + (level * 0.7)
                        color = Qt.rgba(
                            theme.accent.r + (level * 0.2),
                            theme.accent.g + (level * 0.1),
                            theme.accent.b,
                            1.0
                        )
                    }
                }
            }
        }
    }

    Timer {
        id: animationTimer
        interval: 80
        repeat: true
        running: isActive && !isMuted

        onTriggered: {
            generateAudioLevels()
            updateBars()
        }
    }

    function initializeAudioLevels() {
        audioLevels = []
        for (let i = 0; i < barCount; i++) {
            audioLevels.push(0.1)
        }
    }

    function generateAudioLevels() {
        audioLevels = []
        let baseIntensity = currentVolume * 0.8
        let time = Date.now() / 100

        for (let i = 0; i < barCount; i++) {
            let frequency = (i + 1) * 0.2
            let waveComponent1 = Math.sin(time * frequency) * 0.4
            let waveComponent2 = Math.sin(time * frequency * 1.5 + Math.PI/3) * 0.3
            let waveComponent3 = Math.sin(time * frequency * 0.7 + Math.PI/2) * 0.2

            let combinedWave = waveComponent1 + waveComponent2 + waveComponent3
            let randomVariation = (Math.random() - 0.5) * 0.3

            let level = baseIntensity * (0.5 + combinedWave + randomVariation)
            level = Math.max(0.05, Math.min(1.0, level))

            if (isMuted) {
                level = 0.05
            }

            audioLevels.push(level)
        }
    }

    function updateBars() {
        for (let i = 0; i < barsRepeater.count; i++) {
            let bar = barsRepeater.itemAt(i)
            if (bar && i < audioLevels.length) {
                bar.updateBar(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < barsRepeater.count; i++) {
                let bar = barsRepeater.itemAt(i)
                if (bar) {
                    bar.opacity = 1.0
                }
            }
        }
        onExited: {
            for (let i = 0; i < barsRepeater.count; i++) {
                let bar = barsRepeater.itemAt(i)
                if (bar) {
                    bar.opacity = 0.4 + (bar.currentLevel * 0.6)
                }
            }
        }
    }
}
