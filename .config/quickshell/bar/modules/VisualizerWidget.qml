import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int waveCount: 7
    property int maxWaveHeight: 14
    property var audioLevels: []
    property bool isActive: true
    property var cavaData: []
    property bool hasAudioData: false

    Process {
        id: configWriter
        command: ["sh", "-c", "cat > /tmp/cava_quickshell.conf << 'EOF'\n[general]\nbars = 7\nframerate = 30\nsensitivity = 100\n\n[input]\nmethod = pipewire\nsource = alsa_output.pci-0000_30_00.6.analog-stereo\n\n[output]\nmethod = raw\nraw_target = /dev/stdout\ndata_format = ascii\nascii_max_range = 1000\nbar_delimiter = 59\n\n[color]\ngradient = 0\n\n[smoothing]\nmonstercat = 1\nwaves = 0\nnoise_reduction = 0.77\nEOF"]
        running: true

        onExited: function(exitCode) {
            if (exitCode === 0) {
                cavaProcess.running = true
            }
        }
    }

    Process {
        id: cavaProcess
        command: ["cava", "-p", "/tmp/cava_quickshell.conf"]
        running: false
    }

    Connections {
        target: cavaProcess
        function onStdoutChanged() {
            if (cavaProcess.stdout) {
                parseCavaOutput(cavaProcess.stdout)
            }
        }
    }

    Component.onCompleted: {
        initializeAudioLevels()
        animationTimer.start()
    }

    Timer {
        id: animationTimer
        interval: 100
        repeat: true
        running: isActive

        onTriggered: {
            generateAudioLevels()
            updateWaves()
        }
    }

    function initializeAudioLevels() {
        audioLevels = []
        cavaData = []
        for (let i = 0; i < waveCount; i++) {
            audioLevels.push(0.05)
            cavaData.push(0)
        }
    }



    function parseCavaOutput(output) {
        if (!output || output.length === 0) return

        let lines = output.trim().split('\n')
        for (let lineIndex = lines.length - 1; lineIndex >= 0; lineIndex--) {
            let line = lines[lineIndex].trim()
            if (line && line.length > 0) {
                let values = line.split(';')
                if (values.length >= waveCount) {
                    cavaData = []
                    hasAudioData = false

                    for (let i = 0; i < waveCount; i++) {
                        let value = parseInt(values[i]) || 0
                        cavaData.push(value)
                        if (value > 1) hasAudioData = true
                    }

                    generateAudioLevels()
                    return
                }
            }
        }
    }

    function generateAudioLevels() {
        audioLevels = []
        let time = Date.now() / 300

        for (let i = 0; i < waveCount; i++) {
            if (!hasAudioData || cavaData.length === 0) {
                let baseWave = Math.sin(time * 0.5 + i * 0.8) * 0.25
                let secondaryWave = Math.sin(time * 0.8 + i * 0.5) * 0.12
                let randomVariation = (Math.sin(time * 1.5 + i * 1.2) * 0.08)
                let level = 0.15 + baseWave + secondaryWave + randomVariation
                level = Math.max(0.05, Math.min(0.6, level))
                audioLevels.push(level)
            } else {
                let rawValue = cavaData[i] || 0
                let normalizedValue = Math.min(1.0, rawValue / 1000.0)
                let baseLevel = Math.max(0.1, normalizedValue)
                let smoothingWave = Math.sin(time * 0.3 + i * 0.3) * 0.05
                let level = Math.min(1.0, baseLevel + smoothingWave)
                audioLevels.push(level)
            }
        }
    }

    Row {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 3

        Repeater {
            id: wavesRepeater
            model: waveCount

            Item {
                width: 10
                height: maxWaveHeight



                Rectangle {
                    id: waveShape
                    anchors.centerIn: parent
                    width: 8
                    height: 12
                    radius: 2
                    color: theme.accent
                    opacity: 1.0


                    
                    property real currentLevel: 0
                    
                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: parent.width + 4
                        height: parent.height + 2
                        radius: height / 2
                        color: "transparent"
                        border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
                        border.width: 1
                        opacity: parent.opacity * 0.5
                    }

                    Behavior on height {
                        NumberAnimation {
                            duration: 120
                            easing.type: Easing.OutCubic
                        }
                    }

                    Behavior on opacity {
                        NumberAnimation {
                            duration: 200
                        }
                    }

                    Behavior on color {
                        ColorAnimation {
                            duration: 150
                        }
                    }



                    function updateWave(level) {
                        currentLevel = level
                        height = Math.max(6, level * maxWaveHeight)

                        if (!hasAudioData) {
                            opacity = 0.7
                            color = theme.accent
                        } else {
                            opacity = 0.8 + (level * 0.2)
                            let intensity = level
                            color = Qt.rgba(
                                Math.min(1.0, theme.accent.r + (intensity * 0.2)),
                                Math.min(1.0, theme.accent.g + (intensity * 0.15)),
                                Math.min(1.0, theme.accent.b + (intensity * 0.1)),
                                1.0
                            )
                        }
                    }
                }
            }
        }
    }

    function updateWaves() {
        for (let i = 0; i < wavesRepeater.count; i++) {
            let waveItem = wavesRepeater.itemAt(i)
            if (waveItem && waveItem.children[0] && i < audioLevels.length) {
                waveItem.children[0].updateWave(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = Math.min(1.0, wave.opacity + 0.3)
                }
            }
        }
        onExited: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = hasAudioData ? (0.4 + (wave.currentLevel * 0.6)) : 0.2
                }
            }
        }
    }
}
