import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int waveCount: 7
    property int maxWaveHeight: 14
    property var audioLevels: []
    property bool isActive: true
    property var cavaData: []
    property bool hasAudioData: false

    Process {
        id: cavaProcess
        command: ["bash", "-c", "echo '[general]\nbars = 7\nframerate = 10\n[output]\nmethod = raw\nraw_target = /dev/stdout\ndata_format = ascii\nascii_max_range = 10\n[smoothing]\nmonstercat = 1\nwaves = 0' | cava -p /dev/stdin"]
        running: true
    }

    Connections {
        target: cavaProcess
        function onStdoutChanged() {
            parseCavaOutput(cavaProcess.stdout)
        }
    }

    Component.onCompleted: {
        initializeAudioLevels()
        animationTimer.start()
    }

    Timer {
        id: animationTimer
        interval: 50
        repeat: true
        running: isActive

        onTriggered: {
            updateWaves()
        }
    }

    function initializeAudioLevels() {
        audioLevels = []
        cavaData = []
        for (let i = 0; i < waveCount; i++) {
            audioLevels.push(0.05)
            cavaData.push(0)
        }
    }

    function parseCavaOutput(output) {
        if (!output || output.length === 0) return

        let lines = output.trim().split('\n')
        let latestLine = lines[lines.length - 1]

        if (latestLine && latestLine.length > 0) {
            let values = latestLine.split(';')
            if (values.length >= waveCount) {
                cavaData = []
                hasAudioData = false

                for (let i = 0; i < waveCount; i++) {
                    let value = parseInt(values[i]) || 0
                    cavaData.push(value)
                    if (value > 0) hasAudioData = true
                }


                generateAudioLevels()
            }
        }
    }

    function generateAudioLevels() {
        audioLevels = []
        let time = Date.now() / 200

        for (let i = 0; i < waveCount; i++) {
            let cavaValue = (cavaData[i] || 0) / 10.0

            if (!hasAudioData || cavaData.length === 0) {
                let baseWave = Math.sin(time * 0.8 + i * 0.6) * 0.3
                let secondaryWave = Math.sin(time * 1.2 + i * 0.4) * 0.15
                let randomVariation = (Math.sin(time * 2.1 + i * 1.1) * 0.1)
                let level = 0.2 + baseWave + secondaryWave + randomVariation
                level = Math.max(0.05, Math.min(0.8, level))
                audioLevels.push(level)
            } else {
                let baseLevel = Math.max(0.15, cavaValue)
                let waveEffect = Math.sin(time * 0.8 + i * 0.4) * 0.1
                let level = Math.min(1.0, baseLevel + waveEffect)
                audioLevels.push(level)
            }
        }
    }

    Row {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 2

        Repeater {
            id: wavesRepeater
            model: waveCount

            Item {
                width: 8
                height: maxWaveHeight
                
                Rectangle {
                    id: waveShape
                    anchors.centerIn: parent
                    width: 6
                    height: 3
                    radius: height / 2
                    color: theme.accent
                    opacity: 0.6
                    
                    property real currentLevel: 0
                    
                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: parent.width + 4
                        height: parent.height + 2
                        radius: height / 2
                        color: "transparent"
                        border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
                        border.width: 1
                        opacity: parent.opacity * 0.5
                    }

                    Behavior on height {
                        NumberAnimation {
                            duration: 120
                            easing.type: Easing.OutCubic
                        }
                    }

                    Behavior on opacity {
                        NumberAnimation {
                            duration: 200
                        }
                    }

                    Behavior on color {
                        ColorAnimation {
                            duration: 150
                        }
                    }

                    function updateWave(level) {
                        currentLevel = level
                        height = Math.max(3, level * maxWaveHeight)
                        
                        if (!hasAudioData) {
                            opacity = 0.2
                            color = Qt.rgba(theme.accent.r * 0.6, theme.accent.g * 0.6, theme.accent.b * 0.6, 0.6)
                        } else {
                            opacity = 0.4 + (level * 0.6)
                            let intensity = level
                            color = Qt.rgba(
                                Math.min(1.0, theme.accent.r + (intensity * 0.2)),
                                Math.min(1.0, theme.accent.g + (intensity * 0.15)), 
                                Math.min(1.0, theme.accent.b + (intensity * 0.1)),
                                1.0
                            )
                        }
                    }
                }
            }
        }
    }

    function updateWaves() {
        for (let i = 0; i < wavesRepeater.count; i++) {
            let waveItem = wavesRepeater.itemAt(i)
            if (waveItem && waveItem.children[0] && i < audioLevels.length) {
                waveItem.children[0].updateWave(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = Math.min(1.0, wave.opacity + 0.3)
                }
            }
        }
        onExited: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = hasAudioData ? (0.4 + (wave.currentLevel * 0.6)) : 0.2
                }
            }
        }
    }
}
