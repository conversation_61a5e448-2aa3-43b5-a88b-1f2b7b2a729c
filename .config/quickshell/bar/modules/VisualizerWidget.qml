import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int waveCount: 7
    property int maxWaveHeight: 14
    property var audioLevels: []
    property bool isActive: true
    property var cavaData: []
    property bool hasAudioData: false

    Timer {
        id: startTimer
        interval: 100
        running: true
        repeat: false
        onTriggered: {
            cavaProcess.running = true
        }
    }

    Process {
        id: cavaProcess
        command: ["bash", "/home/<USER>/.config/quickshell/cava_parser.sh"]
        running: false
    }

    Connections {
        target: cavaProcess
        function onStdoutChanged() {
            if (cavaProcess.stdout && cavaProcess.stdout.length > 0) {
                parseCavaOutput(cavaProcess.stdout)
            }
        }
        function onStderrChanged() {
            if (cavaProcess.stderr && cavaProcess.stderr.length > 0) {
                console.log("Cava stderr:", cavaProcess.stderr)
            }
        }
    }

    Component.onCompleted: {
        initializeAudioLevels()
        animationTimer.start()
    }

    Timer {
        id: animationTimer
        interval: 100
        repeat: true
        running: isActive

        onTriggered: {
            generateAudioLevels()
            updateWaves()
        }
    }

    function initializeAudioLevels() {
        audioLevels = []
        cavaData = []
        for (let i = 0; i < waveCount; i++) {
            audioLevels.push(0.05)
            cavaData.push(0)
        }
    }



    function parseCavaOutput(output) {
        if (!output || output.length === 0) return

        let lines = output.trim().split('\n')
        let latestLine = ""

        for (let i = lines.length - 1; i >= 0; i--) {
            let line = lines[i].trim()
            if (line && line.length > 0 && !line.includes('\n')) {
                latestLine = line
                break
            }
        }

        if (latestLine) {
            let values = latestLine.split(';')
            if (values.length >= waveCount) {
                let newCavaData = []
                let newHasAudioData = false

                for (let i = 0; i < waveCount; i++) {
                    let value = parseInt(values[i]) || 0
                    newCavaData.push(value)
                    if (value > 0) newHasAudioData = true
                }

                cavaData = newCavaData
                hasAudioData = newHasAudioData
                generateAudioLevels()
            }
        }
    }

    function generateAudioLevels() {
        audioLevels = []
        let time = Date.now() / 400

        for (let i = 0; i < waveCount; i++) {
            if (!hasAudioData || cavaData.length === 0) {
                let baseWave = Math.sin(time * 0.4 + i * 0.7) * 0.2
                let secondaryWave = Math.sin(time * 0.7 + i * 0.4) * 0.1
                let randomVariation = (Math.sin(time * 1.2 + i * 1.0) * 0.06)
                let level = 0.12 + baseWave + secondaryWave + randomVariation
                level = Math.max(0.05, Math.min(0.5, level))
                audioLevels.push(level)
            } else {
                let rawValue = cavaData[i] || 0
                let normalizedValue = Math.min(1.0, rawValue / 8.0)
                let baseLevel = Math.max(0.1, normalizedValue * 0.9)
                let level = Math.min(0.95, baseLevel)
                audioLevels.push(level)
            }
        }
    }

    Row {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 3

        Repeater {
            id: wavesRepeater
            model: waveCount

            Item {
                width: 10
                height: maxWaveHeight



                Rectangle {
                    id: waveShape
                    anchors.centerIn: parent
                    width: 8
                    height: 12
                    radius: 2
                    color: theme.accent
                    opacity: 1.0


                    
                    property real currentLevel: 0
                    
                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: parent.width + 4
                        height: parent.height + 2
                        radius: height / 2
                        color: "transparent"
                        border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
                        border.width: 1
                        opacity: parent.opacity * 0.5
                    }

                    Behavior on height {
                        NumberAnimation {
                            duration: 120
                            easing.type: Easing.OutCubic
                        }
                    }

                    Behavior on opacity {
                        NumberAnimation {
                            duration: 200
                        }
                    }

                    Behavior on color {
                        ColorAnimation {
                            duration: 150
                        }
                    }



                    function updateWave(level) {
                        currentLevel = level
                        height = Math.max(6, level * maxWaveHeight)

                        if (!hasAudioData) {
                            opacity = 0.7
                            color = theme.accent
                        } else {
                            opacity = 0.8 + (level * 0.2)
                            let intensity = level
                            color = Qt.rgba(
                                Math.min(1.0, theme.accent.r + (intensity * 0.2)),
                                Math.min(1.0, theme.accent.g + (intensity * 0.15)),
                                Math.min(1.0, theme.accent.b + (intensity * 0.1)),
                                1.0
                            )
                        }
                    }
                }
            }
        }
    }

    function updateWaves() {
        for (let i = 0; i < wavesRepeater.count; i++) {
            let waveItem = wavesRepeater.itemAt(i)
            if (waveItem && waveItem.children[0] && i < audioLevels.length) {
                waveItem.children[0].updateWave(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = Math.min(1.0, wave.opacity + 0.3)
                }
            }
        }
        onExited: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = hasAudioData ? (0.4 + (wave.currentLevel * 0.6)) : 0.2
                }
            }
        }
    }
}
