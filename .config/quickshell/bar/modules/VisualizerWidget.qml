import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Pipewire
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: visualizerContent.width + 8
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property int waveCount: 7
    property int maxWaveHeight: 14
    property var audioLevels: []
    property bool isActive: true
    property real audioActivity: 0.0
    property real targetActivity: 0.0
    property int silenceCounter: 0
    property bool hasAudioActivity: false

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property real currentVolume: audioNode ? audioNode.volume : 0.0
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    PwObjectTracker {
        objects: [audioSink]
    }

    Process {
        id: audioDetector
        command: ["bash", "-c", "pactl list sink-inputs | grep -E '(State:|Volume:)' | head -20"]
        running: false
    }

    Component.onCompleted: {
        initializeAudioLevels()
        animationTimer.start()
        activityTimer.start()
    }

    Behavior on audioActivity {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    Row {
        id: visualizerContent
        anchors.centerIn: parent
        spacing: 2

        Repeater {
            id: wavesRepeater
            model: waveCount

            Item {
                width: 8
                height: maxWaveHeight

                Rectangle {
                    id: waveShape
                    anchors.centerIn: parent
                    width: 6
                    height: 3
                    radius: height / 2
                    color: theme.accent
                    opacity: 0.6

                    property real targetHeight: 3
                    property real currentLevel: 0
                    property color baseColor: theme.accent

                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: parent.width + 4
                        height: parent.height + 2
                        radius: height / 2
                        color: "transparent"
                        border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
                        border.width: 1
                        opacity: parent.opacity * 0.5
                    }

                    Behavior on height {
                        NumberAnimation {
                            duration: 120
                            easing.type: Easing.OutCubic
                        }
                    }

                    Behavior on opacity {
                        NumberAnimation {
                            duration: 200
                        }
                    }

                    Behavior on color {
                        ColorAnimation {
                            duration: 150
                        }
                    }

                    function updateWave(level) {
                        currentLevel = level
                        targetHeight = Math.max(3, level * maxWaveHeight)
                        height = targetHeight

                        if (isMuted) {
                            opacity = 0.15
                            color = Qt.rgba(theme.textSecondary.r, theme.textSecondary.g, theme.textSecondary.b, 0.4)
                        } else if (!hasAudioActivity && silenceCounter > 4) {
                            opacity = 0.2
                            color = Qt.rgba(theme.accent.r * 0.6, theme.accent.g * 0.6, theme.accent.b * 0.6, 0.6)
                        } else {
                            opacity = 0.4 + (level * 0.6)
                            let intensity = level
                            color = Qt.rgba(
                                Math.min(1.0, theme.accent.r + (intensity * 0.2)),
                                Math.min(1.0, theme.accent.g + (intensity * 0.15)),
                                Math.min(1.0, theme.accent.b + (intensity * 0.1)),
                                1.0
                            )
                        }
                    }
                }
            }
        }
    }

    Timer {
        id: animationTimer
        interval: 60
        repeat: true
        running: isActive

        onTriggered: {
            generateAudioLevels()
            updateWaves()
        }
    }

    Timer {
        id: activityTimer
        interval: 500
        repeat: true
        running: isActive

        onTriggered: {
            checkAudioActivity()
        }
    }

    function initializeAudioLevels() {
        audioLevels = []
        for (let i = 0; i < waveCount; i++) {
            audioLevels.push(0.05)
        }
        audioActivity = 0.05
    }

    function checkAudioActivity() {
        if (isMuted) {
            hasAudioActivity = false
            targetActivity = 0.05
        } else {
            audioDetector.start()
        }
    }

    Connections {
        target: audioDetector
        function onFinished() {
            let output = audioDetector.stdout
            hasAudioActivity = output && output.includes("RUNNING")

            if (hasAudioActivity) {
                targetActivity = Math.max(0.3, currentVolume * 0.8)
                silenceCounter = 0
            } else {
                silenceCounter++
                if (silenceCounter > 4) {
                    targetActivity = 0.05
                }
            }

            audioActivity = audioActivity * 0.7 + targetActivity * 0.3
        }
    }

    function generateAudioLevels() {
        audioLevels = []
        let time = Date.now() / 100

        for (let i = 0; i < waveCount; i++) {
            let level

            if (!hasAudioActivity && silenceCounter > 4) {
                level = 0.05 + Math.sin(time * 0.5 + i * 0.2) * 0.02
            } else {
                let centerIndex = (waveCount - 1) / 2
                let distanceFromCenter = Math.abs(i - centerIndex)
                let centerWeight = 1.0 - (distanceFromCenter / centerIndex) * 0.4

                let frequency = 0.6 + (i * 0.4)
                let waveComponent1 = Math.sin(time * frequency) * 0.4
                let waveComponent2 = Math.sin(time * frequency * 1.2 + Math.PI/3) * 0.3
                let waveComponent3 = Math.sin(time * frequency * 0.8 + Math.PI/2) * 0.2

                let combinedWave = (waveComponent1 + waveComponent2 + waveComponent3) * centerWeight
                let randomVariation = (Math.random() - 0.5) * 0.3 * audioActivity

                level = audioActivity * (0.4 + combinedWave + randomVariation)
                level = Math.max(0.05, Math.min(1.0, level))
            }

            audioLevels.push(level)
        }
    }

    function updateWaves() {
        for (let i = 0; i < wavesRepeater.count; i++) {
            let waveItem = wavesRepeater.itemAt(i)
            if (waveItem && waveItem.children[0] && i < audioLevels.length) {
                waveItem.children[0].updateWave(audioLevels[i])
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = Math.min(1.0, wave.opacity + 0.3)
                }
            }
        }
        onExited: {
            for (let i = 0; i < wavesRepeater.count; i++) {
                let waveItem = wavesRepeater.itemAt(i)
                if (waveItem && waveItem.children[0]) {
                    let wave = waveItem.children[0]
                    wave.opacity = 0.4 + (wave.currentLevel * audioActivity * 0.6)
                }
            }
        }
    }
}
