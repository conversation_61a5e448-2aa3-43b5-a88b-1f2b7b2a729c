import QtQuick

Rectangle {
    id: testClock
    width: 100
    height: 24
    color: clicked ? "red" : "blue"
    radius: 4
    
    property bool clicked: false
    
    Text {
        anchors.centerIn: parent
        text: clicked ? "CLICKED!" : "Click me"
        color: "white"
        font.pixelSize: 12
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: {
            console.log("TestClock clicked!")
            testClock.clicked = !testClock.clicked
        }
    }
    
    Timer {
        interval: 2000
        running: clicked
        onTriggered: clicked = false
    }
}
