import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Pipewire
import "../modules"

Rectangle {
    id: volumeWidget
    objectName: "volumeWidget"
    width: volumeContent.width + 16
    height: 24
    radius: 12
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    visible: Pipewire.defaultAudioSink !== null

    signal showVolumeOverlayRequested()
    signal hideVolumeOverlayRequested()

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property int volumePercent: audioNode ? Math.round(audioNode.volume * 100) : 0
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    Theme {
        id: theme
    }

    RowLayout {
        id: volumeContent
        anchors.centerIn: parent
        spacing: 8

        Text {
            id: volumeIcon
            text: {
                if (isMuted) return "󰖁"
                if (volumePercent === 0) return "󰕿"
                if (volumePercent < 30) return "󰖀"
                if (volumePercent < 70) return "󰕾"
                return "󰕾"
            }
            color: isMuted ? theme.textSecondary : theme.textPrimary
            font.pixelSize: 14
            font.family: "JetBrains Mono Nerd Font, monospace"
        }

        Text {
            id: volumeText
            text: volumePercent + "%"
            color: isMuted ? theme.textSecondary : theme.textPrimary
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"
            font.weight: Font.Medium
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                showVolumeOverlayRequested()
            } else if (mouse.button === Qt.RightButton) {
                launchPavucontrol()
            }
        }
    }

    function launchPavucontrol() {
        var process = Qt.createQmlObject('
            import Quickshell.Io
            Process {
                command: ["pavucontrol"]
                running: true
            }
        ', volumeWidget, "pavucontrolProcess")
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }
}
