import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Pipewire
import "../modules"

Rectangle {
    id: volumeWidget
    objectName: "volumeWidget"
    width: expanded ? expandedWidth : collapsedWidth
    height: 24
    radius: 12
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    visible: Pipewire.defaultAudioSink !== null



    property bool expanded: false
    property int collapsedWidth: volumeContent.width + 16
    property int expandedWidth: volumeContent.width + sliderWidth + 32
    property int sliderWidth: 100

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property real currentVolume: audioNode ? audioNode.volume : 0.0


    readonly property int volumePercent: Math.round(currentVolume * 100)
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    PwObjectTracker {
        objects: [audioSink]
    }







    Theme {
        id: theme
    }

    RowLayout {
        id: volumeContent
        anchors.centerIn: parent
        spacing: 8

        Text {
            id: volumeIcon
            text: {
                if (isMuted) return "󰖁"
                if (volumePercent === 0) return "󰕿"
                if (volumePercent < 30) return "󰖀"
                if (volumePercent < 70) return "󰕾"
                return "󰕾"
            }
            color: isMuted ? theme.textSecondary : theme.textPrimary
            font.pixelSize: 14
            font.family: "JetBrains Mono Nerd Font, monospace"

            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.LeftButton | Qt.RightButton
                onClicked: function(mouse) {
                    if (mouse.button === Qt.LeftButton) {
                        if (audioNode) {
                            audioNode.muted = !audioNode.muted
                        }
                    } else if (mouse.button === Qt.RightButton) {
                        launchPavucontrol()
                    }
                }
            }
        }

        Text {
            id: volumeText
            text: volumePercent + "%"
            color: isMuted ? theme.textSecondary : theme.textPrimary
            font.pixelSize: 10
            font.family: "JetBrains Mono, monospace"
            font.weight: Font.Medium
            visible: !expanded


        }

        Rectangle {
            id: sliderContainer
            width: expanded ? sliderWidth : 0
            height: 4
            radius: 2
            color: theme.backgroundTertiary
            visible: expanded
            opacity: expanded ? 1 : 0
            Layout.alignment: Qt.AlignVCenter

            Rectangle {
                id: sliderFill
                width: parent.width * (volumePercent / 100)
                height: parent.height
                radius: parent.radius
                color: theme.accent

                Behavior on width {
                    NumberAnimation { duration: 150 }
                }
            }

            Repeater {
                model: 7
                Rectangle {
                    width: 1
                    height: parent.height
                    color: index * (parent.width / 6) < sliderFill.width ? theme.accent : theme.textSecondary
                    x: index * (parent.width / 6)
                    opacity: 0.8
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true

                property bool wasDragged: false
                property real startX: 0

                onPressed: function(mouse) {
                    wasDragged = false
                    startX = mouse.x
                }

                onPositionChanged: function(mouse) {
                    if (pressed) {
                        var dragDistance = Math.abs(mouse.x - startX)
                        if (dragDistance > 3) {
                            wasDragged = true
                        }
                        updateVolume(mouse.x)
                    }
                }

                onReleased: function(mouse) {
                    if (!wasDragged) {
                        var clickPosition = mouse.x / width
                        var currentVol = currentVolume
                        var targetVolume

                        if (clickPosition > currentVol) {
                            targetVolume = Math.min(1.0, currentVol + 0.05)
                        } else {
                            targetVolume = Math.max(0.0, currentVol - 0.05)
                        }

                        if (audioNode) {
                            audioNode.volume = targetVolume
                        }
                    }
                }

                function updateVolume(x) {
                    if (audioNode) {
                        var newVolume = Math.max(0, Math.min(1, x / width))
                        audioNode.volume = newVolume
                    }
                }
            }

            Behavior on width {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }

            Behavior on opacity {
                NumberAnimation { duration: 200 }
            }
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            if (mouse.button === Qt.RightButton) {
                launchPavucontrol()
            }
        }

        onWheel: function(wheel) {
            expanded = true
            collapseTimer.restart()

            if (audioNode) {
                var delta = wheel.angleDelta.y / 120
                var volumeChange = delta * 0.05
                var newVolume = Math.max(0.0, Math.min(1.0, currentVolume + volumeChange))
                audioNode.volume = newVolume
            }
        }

        onEntered: {
            if (expanded) {
                collapseTimer.stop()
            }
        }

        onExited: {
            if (expanded) {
                collapseTimer.restart()
            }
        }
    }

    function launchPavucontrol() {
        var process = Qt.createQmlObject('
            import Quickshell.Io
            Process {
                command: ["pavucontrol"]
                running: true
            }
        ', volumeWidget, "pavucontrolProcess")
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: {
            if (!mouseArea.containsMouse) {
                expanded = false
            }
        }
    }

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }
}
