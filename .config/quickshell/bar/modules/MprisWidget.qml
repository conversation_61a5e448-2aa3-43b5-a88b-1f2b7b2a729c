import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import "../modules"

Rectangle {
    id: mprisWidget
    objectName: "mprisWidget"
    width: visible ? (mprisContent.width + 20) : 0
    height: 24
    radius: 6
    color: "transparent"
    
    // Only show if there are active players
    visible: Mpris.players.length > 0
    
    // Signal to communicate with overlay
    signal showMprisOverlayRequested()
    signal hideMprisOverlayRequested()
    
    // Position info for overlay
    readonly property var mprisPosition: ({
        x: mprisWidget.mapToGlobal(0, 0).x,
        y: mprisWidget.mapToGlobal(0, 0).y,
        width: mprisWidget.width,
        height: mprisWidget.height
    })
    
    // Get the first active player (or null if none)
    readonly property var activePlayer: {
        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState !== MprisPlaybackState.Stopped) {
                return player
            }
        }
        // If no playing/paused player, return the first one
        return Mpris.players.length > 0 ? Mpris.players.values[0] : null
    }
    
    // Theme instance
    Theme {
        id: theme
    }
    
    RowLayout {
        id: mprisContent
        anchors.centerIn: parent
        spacing: 8
        
        // Media icon
        Text {
            text: {
                if (!activePlayer) return "󰝚"
                switch (activePlayer.playbackState) {
                    case MprisPlaybackState.Playing: return "󰐊"
                    case MprisPlaybackState.Paused: return "󰏤"
                    case MprisPlaybackState.Stopped: return "󰓛"
                    default: return "󰝚"
                }
            }
            color: theme.textPrimary
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
        }
        
        // Track info (only show if we have an active player with track info)
        Text {
            visible: activePlayer && (activePlayer.trackTitle || activePlayer.trackArtist)
            text: {
                if (!activePlayer) return ""
                var title = activePlayer.trackTitle || "Unknown Title"
                var artist = activePlayer.trackArtist || "Unknown Artist"
                return title + " - " + artist
            }
            color: theme.textPrimary
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"
            
            // Limit width to prevent bar from getting too wide
            elide: Text.ElideRight
            maximumLineCount: 1
            Layout.maximumWidth: 200
        }
    }
    
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true
        
        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                showMprisOverlayRequested()
            } else if (mouse.button === Qt.RightButton && activePlayer) {
                // Right click to toggle play/pause
                activePlayer.togglePlaying()
            }
        }
        
        onEntered: {
            mprisWidget.color = Qt.rgba(0.3, 0.3, 0.3, 0.8)
        }
        
        onExited: {
            mprisWidget.color = "transparent"
        }
    }
    
    // Smooth transitions
    Behavior on width {
        NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
    }
    
    Behavior on color {
        ColorAnimation { duration: 200 }
    }
}
