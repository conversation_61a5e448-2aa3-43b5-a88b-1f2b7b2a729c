import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import "../modules"

Rectangle {
    id: mprisWidget
    objectName: "mprisWidget"
    width: visible ? (mprisContent.width + 16) : 0
    height: 24
    radius: 12
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    // Only show when there are active players
    visible: activePlayer !== null

    // Signals for overlay communication
    signal showMprisOverlayRequested()
    signal hideMprisOverlayRequested()

    // Find the active player (playing or paused)
    readonly property var activePlayer: {
        if (!Mpris.players) return null

        // First, try to find a playing player
        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState === MprisPlaybackState.Playing) {
                return player
            }
        }

        // If no playing player, try to find a paused player
        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState === MprisPlaybackState.Paused) {
                return player
            }
        }
        // If no playing/paused player, return the first one
        return Mpris.players.length > 0 ? Mpris.players.values[0] : null
    }

    // Theme instance
    Theme {
        id: theme
    }

    RowLayout {
        id: mprisContent
        anchors.centerIn: parent
        spacing: 8

        // Play/Pause icon
        Text {
            id: playIcon
            text: {
                if (!activePlayer) return "󰝚"
                switch (activePlayer.playbackState) {
                    case MprisPlaybackState.Playing: return "󰏤"
                    case MprisPlaybackState.Paused: return "󰐊"
                    default: return "󰓛"
                }
            }
            color: theme.textPrimary
            font.pixelSize: 14
            font.family: "JetBrains Mono, monospace"
        }

        // Track info
        Text {
            id: trackInfo
            text: {
                if (!activePlayer) return "No media"
                var title = activePlayer.trackTitle || "Unknown"
                var artist = activePlayer.trackArtists && activePlayer.trackArtists.length > 0
                    ? activePlayer.trackArtists[0] : ""

                if (artist) {
                    return title + " • " + artist
                } else {
                    return title
                }
            }
            color: theme.textSecondary
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"

            // Limit width to prevent bar overflow
            Layout.maximumWidth: 200
            elide: Text.ElideRight
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            console.log("MPRIS clicked with button:", mouse.button)
            if (mouse.button === Qt.LeftButton) {
                console.log("Left click - requesting MPRIS overlay")
                showMprisOverlayRequested()
            } else if (mouse.button === Qt.RightButton && activePlayer) {
                console.log("Right click - toggling play/pause")
                if (activePlayer.playbackState === MprisPlaybackState.Playing) {
                    activePlayer.pause()
                } else {
                    activePlayer.play()
                }
            }
        }
    }

    // Smooth color transition
    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    // Smooth width transition
    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }
}