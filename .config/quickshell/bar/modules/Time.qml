import QtQuick
import "../modules"

Rectangle {
    id: timeModule
    objectName: "timeModule"
    width: timeText.width + 20
    height: 24
    radius: 6
    color: "transparent"

    property date currentDate: new Date()

    Theme {
        id: theme
    }

    Text {
        id: timeText
        anchors.centerIn: parent
        text: Qt.formatDateTime(currentDate, "h:mm AP")
        color: theme.textPrimary
        font.pixelSize: 12
        font.family: "JetBrains Mono, monospace"
        font.weight: Font.Medium
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true

        onEntered: {
            timeModule.color = Qt.rgba(0.3, 0.3, 0.3, 0.8)
        }

        onExited: {
            timeModule.color = "transparent"
        }
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }
}
