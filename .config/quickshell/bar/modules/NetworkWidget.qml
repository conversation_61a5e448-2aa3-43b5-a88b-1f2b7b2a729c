import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: networkWidget
    width: expanded ? expandedWidth : collapsedWidth
    height: 18
    radius: 9
    color: "transparent"

    property bool expanded: false
    property int collapsedWidth: networkIcon.width + 16
    property int expandedWidth: 180

    property string connectionType: "disconnected"
    property string networkIcon: "󰖪"
    property string essid: ""
    property int signalStrength: 0
    property string ipAddress: ""
    property real downloadSpeed: 0
    property real uploadSpeed: 0

    Theme {
        id: theme
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: expanded = false
    }

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }

    Process {
        id: networkStatusProcess
        command: ["bash", "-c", "nmcli -t -f TYPE,DEVICE,STATE connection show --active | head -1"]
        running: true

        onExited: {
            if (exitCode === 0) {
                var output = stdout.trim()
                if (output) {
                    var parts = output.split(':')
                    if (parts.length >= 3) {
                        var type = parts[0]
                        var state = parts[2]
                        
                        if (state === "activated") {
                            if (type === "802-11-wireless" || type === "wifi") {
                                connectionType = "wifi"
                                networkIcon = "󰤨"
                                getWifiInfo()
                            } else if (type === "802-3-ethernet" || type === "ethernet") {
                                connectionType = "ethernet"
                                networkIcon = "󰈀"
                            }
                        }
                    }
                } else {
                    connectionType = "disconnected"
                    networkIcon = "󰖪"
                }
            }
            
            if (connectionType !== "disconnected") {
                getNetworkStats()
            }
        }
    }

    Process {
        id: wifiInfoProcess
        command: ["bash", "-c", "nmcli -t -f SSID,SIGNAL dev wifi list --rescan no | grep '^[^:]*:[0-9]*$' | head -1"]
        
        onExited: {
            if (exitCode === 0 && stdout.trim()) {
                var parts = stdout.trim().split(':')
                if (parts.length >= 2) {
                    essid = parts[0]
                    signalStrength = parseInt(parts[1]) || 0
                    
                    if (signalStrength >= 75) networkIcon = "󰤨"
                    else if (signalStrength >= 50) networkIcon = "󰤥"
                    else if (signalStrength >= 25) networkIcon = "󰤢"
                    else networkIcon = "󰤟"
                }
            }
        }
    }

    Process {
        id: networkStatsProcess
        command: ["bash", "-c", "cat /proc/net/dev | grep -E '(wlan|eth|enp)' | head -1 | awk '{print $2,$10}'"]
        
        property real lastRxBytes: 0
        property real lastTxBytes: 0
        property real lastTime: 0
        
        onExited: {
            if (exitCode === 0 && stdout.trim()) {
                var parts = stdout.trim().split(' ')
                if (parts.length >= 2) {
                    var currentTime = Date.now() / 1000
                    var rxBytes = parseFloat(parts[0])
                    var txBytes = parseFloat(parts[1])
                    
                    if (lastTime > 0) {
                        var timeDiff = currentTime - lastTime
                        if (timeDiff > 0) {
                            downloadSpeed = (rxBytes - lastRxBytes) / timeDiff
                            uploadSpeed = (txBytes - lastTxBytes) / timeDiff
                        }
                    }
                    
                    lastRxBytes = rxBytes
                    lastTxBytes = txBytes
                    lastTime = currentTime
                }
            }
        }
    }

    Timer {
        interval: 5000
        running: true
        repeat: true
        onTriggered: {
            networkStatusProcess.start()
        }
    }

    Timer {
        interval: 2000
        running: connectionType !== "disconnected"
        repeat: true
        onTriggered: {
            networkStatsProcess.start()
        }
    }

    function getWifiInfo() {
        wifiInfoProcess.start()
    }

    function getNetworkStats() {
        networkStatsProcess.start()
    }

    function formatBytes(bytes) {
        if (bytes < 1024) return bytes.toFixed(0) + " B/s"
        else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB/s"
        else return (bytes / (1024 * 1024)).toFixed(1) + " MB/s"
    }

    RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 8
        anchors.rightMargin: 8
        spacing: 8

        Text {
            id: networkIcon
            text: networkWidget.networkIcon
            color: connectionType === "disconnected" ? theme.textSecondary : theme.accent
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
            Layout.alignment: Qt.AlignVCenter
        }

        Rectangle {
            id: bandwidthContainer
            width: expanded ? bandwidthContent.width : 0
            height: parent.height
            color: "transparent"
            visible: expanded && connectionType !== "disconnected"
            clip: true
            Layout.alignment: Qt.AlignVCenter

            Row {
                id: bandwidthContent
                anchors.verticalCenter: parent.verticalCenter
                spacing: 8

                Text {
                    text: "󰇚"
                    color: "#99ffdd"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono Nerd Font, monospace"
                }

                Text {
                    text: formatBytes(downloadSpeed)
                    color: "#99ffdd"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }

                Text {
                    text: "󰕒"
                    color: "#ffcc66"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono Nerd Font, monospace"
                }

                Text {
                    text: formatBytes(uploadSpeed)
                    color: "#ffcc66"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }
            }

            Behavior on width {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onClicked: {
            expanded = !expanded
        }
        onEntered: {
            if (expanded) {
                collapseTimer.stop()
            }
        }
        onExited: {
            if (expanded) {
                collapseTimer.start()
            }
        }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

    Component.onCompleted: {
        networkStatusProcess.start()
    }
}
