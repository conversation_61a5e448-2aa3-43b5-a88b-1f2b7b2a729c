import QtQuick
import QtQuick.Layouts

Rectangle {
    id: networkWidget
    width: expanded ? expandedWidth : collapsedWidth
    height: 18
    radius: 9
    color: "transparent"

    property bool expanded: false
    property int collapsedWidth: 32
    property int expandedWidth: 180

    property string connectionType: "wifi"
    property string networkIcon: "󰤨"
    property real downloadSpeed: 2.5 * 1024
    property real uploadSpeed: 180

    Theme {
        id: theme
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: expanded = false
    }

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }



    function formatBytes(bytes) {
        if (bytes < 1024) return bytes.toFixed(0) + " B/s"
        else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB/s"
        else return (bytes / (1024 * 1024)).toFixed(1) + " MB/s"
    }

    RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 8
        anchors.rightMargin: 8
        spacing: 8

        Text {
            id: networkIconText
            text: networkWidget.networkIcon
            color: connectionType === "disconnected" ? theme.textSecondary : theme.accent
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
            Layout.alignment: Qt.AlignVCenter
        }

        Rectangle {
            id: bandwidthContainer
            width: expanded ? bandwidthContent.width : 0
            height: parent.height
            color: "transparent"
            visible: expanded && connectionType !== "disconnected"
            clip: true
            Layout.alignment: Qt.AlignVCenter

            Row {
                id: bandwidthContent
                anchors.verticalCenter: parent.verticalCenter
                spacing: 4

                Text {
                    text: "󰇚"
                    color: "#99ffdd"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono Nerd Font, monospace"
                }

                Text {
                    text: formatBytes(downloadSpeed)
                    color: "#99ffdd"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }

                Text {
                    text: "󰕒"
                    color: "#ffcc66"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono Nerd Font, monospace"
                }

                Text {
                    text: formatBytes(uploadSpeed)
                    color: "#ffcc66"
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }
            }

            Behavior on width {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onClicked: {
            expanded = !expanded
        }
        onEntered: {
            if (expanded) {
                collapseTimer.stop()
            }
        }
        onExited: {
            if (expanded) {
                collapseTimer.start()
            }
        }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

    Component.onCompleted: {
        console.log("NETWORK DEBUG: NetworkWidget created!")
    }
}
