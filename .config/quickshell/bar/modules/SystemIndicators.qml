import QtQuick
import QtQuick.Layouts

Rectangle {
    id: systemIndicators
    width: indicatorContent.width + 16
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    // Theme instance
    Theme {
        id: theme
    }

    RowLayout {
        id: indicatorContent
        anchors.centerIn: parent
        spacing: 8

        // System indicators can be added here (battery, etc.)
        Text {
            text: "󰍹"
            color: theme.textSecondary
            font.pixelSize: 12
            font.family: "JetBrains Mono Nerd Font, monospace"
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }
}
