import QtQuick
import QtQuick.Layouts

Rectangle {
    id: systemIndicators
    width: indicatorContent.width + 4
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    // Theme instance
    Theme {
        id: theme
    }

    RowLayout {
        id: indicatorContent
        anchors.centerIn: parent
        spacing: 8

        // Empty for now - can add battery or other system indicators later
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }
}
