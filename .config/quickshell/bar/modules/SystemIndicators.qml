import QtQuick
import QtQuick.Layouts

RowLayout {
    id: systemIndicators
    spacing: 8
    implicitHeight: 18
    
    // Theme instance
    Theme {
        id: theme
    }
    

    
    // Network indicator (circular)
    Rectangle {
        id: networkIndicator
        width: 14
        height: 14
        radius: 7
        color: theme.statusGood
        
        Text {
            anchors.centerIn: parent
            text: "󰤨"
            color: theme.textPrimary
            font.pixelSize: 8
            font.family: "JetBrains Mono Nerd Font, monospace"
        }
        
        MouseArea {
            anchors.fill: parent
            hoverEnabled: true
            onEntered: {
                parent.scale = 1.15
                parent.color = Qt.lighter(theme.statusGood, 1.2)
            }
            onExited: {
                parent.scale = 1.0
                parent.color = theme.statusGood
            }
        }
        
        Behavior on scale {
            NumberAnimation { duration: 150 }
        }
        
        Behavior on color {
            ColorAnimation { duration: 300 }
        }
    }

}
