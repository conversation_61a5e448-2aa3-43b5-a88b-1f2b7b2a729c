import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.Mpris
import Quickshell.Widgets
import "./modules"

PanelWindow {
    id: overlayWindow
    
    property var barWindow: null
    property bool overlayVisible: false
    
    // Animation properties
    property int animDuration: 300
    property bool isAnimating: false
    
    // Position as a small centered window
    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    // Fixed size for the MPRIS overlay
    implicitWidth: overlayVisible ? 350 : 100
    implicitHeight: overlayVisible ? 200 : 0

    // Center the overlay horizontally with calculated margins
    margins {
        top: 30  // Just below the bar
        left: Math.max(0, (1920 - implicitWidth) / 2)  // Center on main screen
        right: Math.max(0, (1920 - implicitWidth) / 2)  // Center on main screen
    }

    // Only visible when overlay is active
    visible: overlayVisible

    // Layer shell properties
    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusionMode: ExclusionMode.Ignore
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.None
    
    // Smooth morphing animations
    Behavior on width {
        NumberAnimation {
            duration: animDuration
            easing.type: Easing.OutBack
        }
    }
    
    Behavior on height {
        NumberAnimation {
            duration: animDuration
            easing.type: Easing.OutBack
        }
    }
    
    // Trigger animation state
    onOverlayVisibleChanged: {
        if (overlayVisible) {
            isAnimating = true
            animationTimer.restart()
        }
    }
    
    Timer {
        id: animationTimer
        interval: animDuration + 50
        onTriggered: overlayWindow.isAnimating = false
    }

    // Get the active player
    readonly property var activePlayer: {
        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState !== MprisPlaybackState.Stopped) {
                return player
            }
        }
        return Mpris.players.length > 0 ? Mpris.players.values[0] : null
    }

    // Theme instance
    Theme {
        id: theme
    }

    // Main overlay content
    Rectangle {
        anchors.fill: parent
        color: theme.background
        radius: 12
        border.color: theme.textSecondary
        border.width: 1
        
        // Drop shadow effect
        Rectangle {
            anchors.fill: parent
            anchors.margins: -2
            color: "transparent"
            radius: parent.radius + 2
            border.color: Qt.rgba(0, 0, 0, 0.3)
            border.width: 1
            z: -1
        }
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12
            
            // Header with player info
            RowLayout {
                Layout.fillWidth: true
                spacing: 12
                
                // Album art placeholder or icon
                Rectangle {
                    width: 48
                    height: 48
                    radius: 6
                    color: theme.textSecondary
                    
                    // Try to load album art if available
                    Image {
                        anchors.fill: parent
                        anchors.margins: 2
                        source: activePlayer && activePlayer.trackArtUrl ? activePlayer.trackArtUrl : ""
                        visible: activePlayer && activePlayer.trackArtUrl
                        fillMode: Image.PreserveAspectCrop
                    }
                    
                    // Fallback icon
                    Text {
                        anchors.centerIn: parent
                        text: "󰝚"
                        color: theme.textPrimary
                        font.pixelSize: 24
                        font.family: "JetBrains Mono Nerd Font, monospace"
                        visible: !activePlayer || !activePlayer.trackArtUrl
                    }
                }
                
                // Track info
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 4
                    
                    Text {
                        text: activePlayer ? (activePlayer.trackTitle || "Unknown Title") : "No Media Playing"
                        color: theme.textPrimary
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: activePlayer ? (activePlayer.trackArtist || "Unknown Artist") : ""
                        color: theme.textSecondary
                        font.pixelSize: 12
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: activePlayer ? (activePlayer.trackAlbum || "") : ""
                        color: theme.textSecondary
                        font.pixelSize: 10
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                        visible: text !== ""
                    }
                }
            }
            
            // Progress bar (if position is supported)
            RowLayout {
                Layout.fillWidth: true
                visible: activePlayer && activePlayer.positionSupported && activePlayer.lengthSupported
                spacing: 8
                
                Text {
                    text: activePlayer ? formatTime(activePlayer.position) : "0:00"
                    color: theme.textSecondary
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }
                
                Rectangle {
                    Layout.fillWidth: true
                    height: 4
                    radius: 2
                    color: theme.textSecondary
                    
                    Rectangle {
                        width: activePlayer && activePlayer.length > 0 ? 
                               (activePlayer.position / activePlayer.length) * parent.width : 0
                        height: parent.height
                        radius: parent.radius
                        color: theme.textPrimary
                    }
                }
                
                Text {
                    text: activePlayer ? formatTime(activePlayer.length) : "0:00"
                    color: theme.textSecondary
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                }
            }
            
            // Control buttons
            RowLayout {
                Layout.alignment: Qt.AlignHCenter
                spacing: 16
                
                // Previous button
                Rectangle {
                    width: 32
                    height: 32
                    radius: 16
                    color: activePlayer && activePlayer.canGoPrevious ? 
                           theme.indicatorActive : theme.indicatorMuted
                    
                    Text {
                        anchors.centerIn: parent
                        text: "󰒮"
                        color: theme.textPrimary
                        font.pixelSize: 16
                        font.family: "JetBrains Mono Nerd Font, monospace"
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        enabled: activePlayer && activePlayer.canGoPrevious
                        onClicked: if (activePlayer) activePlayer.previous()
                        
                        onPressed: parent.scale = 0.95
                        onReleased: parent.scale = 1.0
                    }
                    
                    Behavior on scale {
                        NumberAnimation { duration: 100 }
                    }
                }
                
                // Play/Pause button
                Rectangle {
                    width: 40
                    height: 40
                    radius: 20
                    color: activePlayer && activePlayer.canTogglePlaying ? 
                           theme.indicatorActive : theme.indicatorMuted
                    
                    Text {
                        anchors.centerIn: parent
                        text: {
                            if (!activePlayer) return "󰐊"
                            return activePlayer.isPlaying ? "󰏤" : "󰐊"
                        }
                        color: theme.textPrimary
                        font.pixelSize: 20
                        font.family: "JetBrains Mono Nerd Font, monospace"
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        enabled: activePlayer && activePlayer.canTogglePlaying
                        onClicked: if (activePlayer) activePlayer.togglePlaying()
                        
                        onPressed: parent.scale = 0.95
                        onReleased: parent.scale = 1.0
                    }
                    
                    Behavior on scale {
                        NumberAnimation { duration: 100 }
                    }
                }
                
                // Next button
                Rectangle {
                    width: 32
                    height: 32
                    radius: 16
                    color: activePlayer && activePlayer.canGoNext ? 
                           theme.indicatorActive : theme.indicatorMuted
                    
                    Text {
                        anchors.centerIn: parent
                        text: "󰒭"
                        color: theme.textPrimary
                        font.pixelSize: 16
                        font.family: "JetBrains Mono Nerd Font, monospace"
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        enabled: activePlayer && activePlayer.canGoNext
                        onClicked: if (activePlayer) activePlayer.next()
                        
                        onPressed: parent.scale = 0.95
                        onReleased: parent.scale = 1.0
                    }
                    
                    Behavior on scale {
                        NumberAnimation { duration: 100 }
                    }
                }
            }
        }
    }
    
    // Helper function to format time
    function formatTime(seconds) {
        if (!seconds || seconds < 0) return "0:00"
        var mins = Math.floor(seconds / 60)
        var secs = Math.floor(seconds % 60)
        return mins + ":" + (secs < 10 ? "0" : "") + secs
    }
}
