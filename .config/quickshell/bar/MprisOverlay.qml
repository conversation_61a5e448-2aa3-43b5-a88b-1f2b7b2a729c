import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Services.Mpris
import Quickshell.Widgets
import "./modules"

PanelWindow {
    id: mprisOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false

    readonly property var activePlayer: {
        if (!Mpris.players) return null

        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState === MprisPlaybackState.Playing) {
                return player
            }
        }

        for (var i = 0; i < Mpris.players.length; i++) {
            var player = Mpris.players.values[i]
            if (player && player.playbackState === MprisPlaybackState.Paused) {
                return player
            }
        }
        return Mpris.players.length > 0 ? Mpris.players.values[0] : null
    }

    visible: overlayVisible && activePlayer !== null

    anchors {
        top: barWindow.bottom
        topMargin: 8
        horizontalCenter: barWindow.horizontalCenter
    }

    width: 400
    height: 280

    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.keyboardInteractivity: KeyboardInteractivity.None

    Theme {
        id: theme
    }

    Rectangle {
        id: overlayBackground
        anchors.fill: parent
        radius: 16
        color: theme.backgroundSecondary
        border.color: theme.border
        border.width: 1

        Rectangle {
            anchors.fill: parent
            anchors.margins: -4
            radius: parent.radius + 4
            color: "transparent"
            border.color: Qt.rgba(0, 0, 0, 0.2)
            border.width: 1
            z: -1
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 16

            Text {
                text: activePlayer ? (activePlayer.identity || "Media Player") : "No Player"
                color: theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignHCenter
            }

            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 20

                Rectangle {
                    Layout.preferredWidth: 120
                    Layout.preferredHeight: 120
                    radius: 8
                    color: theme.backgroundTertiary
                    border.color: theme.border
                    border.width: 1

                    Image {
                        anchors.fill: parent
                        anchors.margins: 2
                        source: activePlayer && activePlayer.trackArtUrl ? activePlayer.trackArtUrl : ""
                        fillMode: Image.PreserveAspectCrop
                        visible: activePlayer && activePlayer.trackArtUrl
                        radius: 6
                    }

                    Text {
                        anchors.centerIn: parent
                        text: "󰝚"
                        color: theme.textSecondary
                        font.pixelSize: 32
                        font.family: "JetBrains Mono Nerd Font, monospace"
                        visible: !activePlayer || !activePlayer.trackArtUrl
                    }
                }

                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 12

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 4

                        Text {
                            text: activePlayer ? (activePlayer.trackTitle || "Unknown Title") : "No Track"
                            color: theme.textPrimary
                            font.pixelSize: 16
                            font.family: "JetBrains Mono, monospace"
                            font.weight: Font.Bold
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                        }

                        Text {
                            text: activePlayer && activePlayer.trackArtists && activePlayer.trackArtists.length > 0
                                ? activePlayer.trackArtists[0] : "Unknown Artist"
                            color: theme.textSecondary
                            font.pixelSize: 14
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                        }

                        Text {
                            text: activePlayer ? (activePlayer.trackAlbum || "") : ""
                            color: theme.textTertiary
                            font.pixelSize: 12
                            font.family: "JetBrains Mono, monospace"
                            elide: Text.ElideRight
                            Layout.fillWidth: true
                            visible: text !== ""
                        }
                    }

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 4
                        visible: activePlayer && activePlayer.length > 0

                        Rectangle {
                            Layout.fillWidth: true
                            height: 4
                            radius: 2
                            color: theme.backgroundTertiary

                            Rectangle {
                                width: activePlayer && activePlayer.length > 0
                                    ? (activePlayer.position / activePlayer.length) * parent.width : 0
                                height: parent.height
                                radius: parent.radius
                                color: theme.accent
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: activePlayer ? formatTime(activePlayer.position) : "0:00"
                                color: theme.textTertiary
                                font.pixelSize: 10
                                font.family: "JetBrains Mono, monospace"
                            }

                            Item { Layout.fillWidth: true }

                            Text {
                                text: activePlayer ? formatTime(activePlayer.length) : "0:00"
                                color: theme.textTertiary
                                font.pixelSize: 10
                                font.family: "JetBrains Mono, monospace"
                            }
                        }
                    }

                    RowLayout {
                        Layout.alignment: Qt.AlignHCenter
                        spacing: 16

                        Rectangle {
                            width: 40
                            height: 40
                            radius: 20
                            color: prevMouseArea.containsMouse ? theme.backgroundTertiary : "transparent"
                            border.color: theme.border
                            border.width: 1

                            Text {
                                anchors.centerIn: parent
                                text: "󰒮"
                                color: activePlayer && activePlayer.canGoPrevious ? theme.textPrimary : theme.textTertiary
                                font.pixelSize: 16
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: prevMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoPrevious
                                onClicked: if (activePlayer) activePlayer.previous()
                            }
                        }

                        Rectangle {
                            width: 50
                            height: 50
                            radius: 25
                            color: playMouseArea.containsMouse ? theme.accent : theme.backgroundTertiary
                            border.color: theme.accent
                            border.width: 2

                            Text {
                                anchors.centerIn: parent
                                text: {
                                    if (!activePlayer) return "󰐊"
                                    return activePlayer.playbackState === MprisPlaybackState.Playing ? "󰏤" : "󰐊"
                                }
                                color: playMouseArea.containsMouse ? theme.backgroundPrimary : theme.textPrimary
                                font.pixelSize: 20
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: playMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: {
                                    if (activePlayer) {
                                        if (activePlayer.playbackState === MprisPlaybackState.Playing) {
                                            activePlayer.pause()
                                        } else {
                                            activePlayer.play()
                                        }
                                    }
                                }
                            }
                        }

                        Rectangle {
                            width: 40
                            height: 40
                            radius: 20
                            color: nextMouseArea.containsMouse ? theme.backgroundTertiary : "transparent"
                            border.color: theme.border
                            border.width: 1

                            Text {
                                anchors.centerIn: parent
                                text: "󰒭"
                                color: activePlayer && activePlayer.canGoNext ? theme.textPrimary : theme.textTertiary
                                font.pixelSize: 16
                                font.family: "JetBrains Mono Nerd Font, monospace"
                            }

                            MouseArea {
                                id: nextMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                enabled: activePlayer && activePlayer.canGoNext
                                onClicked: if (activePlayer) activePlayer.next()
                            }
                        }
                    }
                }
            }
        }
    }

    function formatTime(seconds) {
        if (!seconds || seconds <= 0) return "0:00"
        var mins = Math.floor(seconds / 60)
        var secs = Math.floor(seconds % 60)
        return mins + ":" + (secs < 10 ? "0" : "") + secs
    }

    opacity: overlayVisible ? 1.0 : 0.0

    Behavior on opacity {
        NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
    }
}