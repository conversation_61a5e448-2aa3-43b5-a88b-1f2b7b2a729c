import QtQuick

QtObject {
    id: config

    property var workspaceRanges: [
        [1, 6],
        [7, 9]
    ]

    property var screenConfigs: [
        {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        },
        {
            left: ["workspaces"],
            center: ["time"],
            right: ["system"]
        }
    ]

    property var availableModules: {
        "workspaces": "HyprlandWorkspaces",
        "time": "Time",
        "system": "SystemIndicators",
        "mpris": "MprisWidget",
        "volume": "VolumeWidget"
    }

    function getScreenConfig(screenIndex) {
        if (screenIndex < screenConfigs.length) {
            return screenConfigs[screenIndex]
        }
        return {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        }
    }

    function getWorkspaceRange(screenIndex) {
        if (screenIndex < workspaceRanges.length) {
            return workspaceRanges[screenIndex]
        }
        return [1, 10]
    }
}
