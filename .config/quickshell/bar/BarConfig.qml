import QtQuick

QtObject {
    id: config
    
    // Multi-monitor workspace configuration
    property var workspaceRanges: [
        [1, 6],  // Screen 0: workspaces 1-6
        [7, 9]   // Screen 1: workspaces 7-9
    ]
    
    // Module configuration for each screen
    property var screenConfigs: [
        // Screen 0 (Primary)
        {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        },
        // Screen 1 (Secondary)
        {
            left: ["workspaces"],
            center: ["time"],
            right: ["system"]
        }
    ]
    
    // Available modules
    property var availableModules: {
        "workspaces": "HyprlandWorkspaces",
        "time": "Time",
        "system": "SystemIndicators",
        "mpris": "MprisWidget"
    }
    
    // Get configuration for specific screen
    function getScreenConfig(screenIndex) {
        if (screenIndex < screenConfigs.length) {
            return screenConfigs[screenIndex]
        }
        // Default config for additional screens
        return {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        }
    }
    
    // Get workspace range for specific screen
    function getWorkspaceRange(screenIndex) {
        if (screenIndex < workspaceRanges.length) {
            return workspaceRanges[screenIndex]
        }
        // Default range for additional screens
        return [1, 10]
    }
}
