import QtQuick
import Quickshell
import Quickshell.Wayland

PanelWindow {
    id: window

    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0

    screen: targetScreen
    
    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    implicitHeight: 40
    margins {
        left: 0
        right: 0
        top: 0
    }

    visible: true
    color: "red"  // Make it very obvious
    
    Rectangle {
        anchors.fill: parent
        color: "blue"
        radius: 8
        
        Text {
            anchors.centerIn: parent
            text: "TEST BAR - SCREEN " + screenIndex + " (" + (targetScreen ? targetScreen.name : "UNKNOWN") + ")"
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }
}
