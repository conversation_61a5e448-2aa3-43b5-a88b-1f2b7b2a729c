import QtQuick
import Quickshell
import "bar"

ShellRoot {
    Component.onCompleted: {
        console.log("=== SHELL DEBUG ===")
        console.log("Available screens:", Quickshell.screens.length)
        for (var i = 0; i < Quickshell.screens.length; i++) {
            var screen = Quickshell.screens[i]
            console.log("Screen", i + ":", screen.name, "Size:", screen.width + "x" + screen.height)
        }
        console.log("==================")
    }

    // Create bars for all available screens
    Bar {
        id: bar0
        targetScreen: Quickshell.screens[0]
        screenIndex: 0

        Component.onCompleted: {
            console.log("Creating bar for screen 0:", targetScreen.name)
        }
    }

    Bar {
        id: bar1
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1

        Component.onCompleted: {
            console.log("Creating bar for screen 1:", targetScreen.name)
        }
    }

    // MPRIS overlays for each screen
    Loader {
        id: mprisOverlay0
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens[0]
            item.barWindow = bar0
            item.overlayVisible = Qt.binding(() => bar0.mprisOverlayVisible)
        }
    }

    Loader {
        id: mprisOverlay1
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
            item.barWindow = bar1
            item.overlayVisible = Qt.binding(() => bar1.mprisOverlayVisible)
        }
    }

}
