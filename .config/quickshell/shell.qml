import QtQuick
import Quickshell
import "bar"

ShellRoot {
    Component.onCompleted: {
        console.log("=== SHELL DEBUG ===")
        console.log("Available screens:", Quickshell.screens.length)
        for (var i = 0; i < Quickshell.screens.length; i++) {
            var screen = Quickshell.screens[i]
            console.log("Screen", i + ":", screen.name, "Size:", screen.width + "x" + screen.height)
        }
        console.log("==================")
    }

    // Create bars for all available screens
    Bar {
        id: bar0
        targetScreen: Quickshell.screens[0]
        screenIndex: 0

        Component.onCompleted: {
            console.log("Creating bar for screen 0:", targetScreen.name)
        }
    }

    Bar {
        id: bar1
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1

        Component.onCompleted: {
            console.log("Creating bar for screen 1:", targetScreen.name)
        }
    }

    // Clock overlays for each screen
    ClockOverlay {
        id: overlay0
        screen: Quickshell.screens[0]
        barWindow: bar0
        overlayVisible: bar0.overlayVisible
        overlayMode: bar0.overlayMode
    }

    ClockOverlay {
        id: overlay1
        screen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        barWindow: bar1
        overlayVisible: bar1.overlayVisible
        overlayMode: bar1.overlayMode
    }


}
