import QtQuick
import Quickshell
import "bar"

ShellRoot {
    Component.onCompleted: {
    }

    Bar {
        id: bar0
        targetScreen: Quickshell.screens[0]
        screenIndex: 0

        Component.onCompleted: {
        }
    }

    Bar {
        id: bar1
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1

        Component.onCompleted: {
        }
    }
    Loader {
        id: mprisOverlay0
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens[0]
            item.barWindow = bar0
            item.overlayVisible = Qt.binding(() => bar0.mprisOverlayVisible)
        }
    }

    Loader {
        id: mprisOverlay1
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
            item.barWindow = bar1
            item.overlayVisible = Qt.binding(() => bar1.mprisOverlayVisible)
        }
    }

    Loader {
        id: volumeOverlay0
        source: "bar/VolumeOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens[0]
            item.barWindow = bar0
            item.overlayVisible = Qt.binding(() => bar0.volumeOverlayVisible)
        }
    }

    Loader {
        id: volumeOverlay1
        source: "bar/VolumeOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
            item.barWindow = bar1
            item.overlayVisible = Qt.binding(() => bar1.volumeOverlayVisible)
        }
    }

}
